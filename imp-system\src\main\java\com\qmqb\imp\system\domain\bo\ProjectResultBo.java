package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.dto.BasePageDTO;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 项目成果表业务对象 tb_project_result
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectResultBo extends BasePageDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务类型ID，关联业务类型管理表主键
     */
    @NotNull(message = "业务类型ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long businessTypeId;

    /**
     * 成果编号（系统自动生成，不允许修改）
     */
    private String resultCode;

    /**
     * 成果类型：1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它
     */
    @NotBlank(message = "成果类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String resultType;

    /**
     * 项目/任务名称，限制30字符
     */
    @NotBlank(message = "项目/任务名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 30, message = "项目/任务名称不能超过30个字符", groups = {AddGroup.class, EditGroup.class})
    private String projectTaskName;

    /**
     * 优先级：P1/P2/P3
     */
    @NotBlank(message = "优先级不能为空", groups = {AddGroup.class, EditGroup.class})
    private String priorityLevel;

    /**
     * 状态：1未开始、2进行中、3已完成、4已取消
     */
    @NotBlank(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 完成评审时间
     */
    private Date milestoneRequirements;

    /**
     * 完成开发时间
     */
    private Date milestoneDevelopment;

    /**
     * 完成测试验收时间
     */
    private Date milestoneTest;

    /**
     * 完成上线时间
     */
    private Date milestoneOnline;

    /**
     * 需求评审进度百分比
     */
    @DecimalMin(value = "0", message = "需求评审进度不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "100", message = "需求评审进度不能大于100", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal requirementsProgress;

    /**
     * 开发进度百分比
     */
    @DecimalMin(value = "0", message = "开发进度不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "100", message = "开发进度不能大于100", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal developmentProgress;

    /**
     * 测试验收进度百分比
     */
    @DecimalMin(value = "0", message = "测试验收进度不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "100", message = "测试验收进度不能大于100", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal testProgress;

    /**
     * 开发组，多个用逗号分隔
     */
    private String devTeams;

    /**
     * 测试组，多个用逗号分隔
     */
    private String testTeams;

    /**
     * 产品经理(需求创建人)，多个用逗号分隔
     */
    private String productManagers;

    /**
     * 开发投入人力（人）
     */
    @Min(value = 0, message = "开发投入人力不能为负数", groups = {AddGroup.class, EditGroup.class})
    private Integer devManpower;

    /**
     * 测试投入人力（人）
     */
    @Min(value = 0, message = "测试投入人力不能为负数", groups = {AddGroup.class, EditGroup.class})
    private Integer testManpower;

    /**
     * 开发工作量（人日）
     */
    @DecimalMin(value = "0", message = "开发工作量不能为负数", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal devWorkload;

    /**
     * 测试工作量（人日）
     */
    @DecimalMin(value = "0", message = "测试工作量不能为负数", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal testWorkload;

    /**
     * 需求背景
     */
    private String requirementBackground;

    /**
     * 负责项目经理，多个用逗号分隔
     */
    private String projectManagers;

    /**
     * 完成时间，仅状态为已完成时可填写
     */
    private Date completionTime;

    /**
     * 归档标志：0未归档、1已归档
     */
    private Integer archiveFlag;

    /**
     * 所属业务大类：1国内、2海外（用于查询条件，通过关联表查询）
     */
    private String businessCategoryMajor;

    /**
     * 所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它（用于查询条件，通过关联表查询）
     */
    private String businessCategoryMinor;

    /**
     * 创建时间(开始)
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间(结束)
     */
    private LocalDateTime createTimeEnd;

    /**
     * 需求id列表
     */
    private List<Long> storyIdList;
}
