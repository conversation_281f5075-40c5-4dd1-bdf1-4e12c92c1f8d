package com.qmqb.imp.system.service;

/**
 * 项目成果流水号服务接口
 * 
 * <AUTHOR>
 */
public interface IProjectResultNumberService {
    
    /**
     * 获取下一个流水号
     * 
     * @param dateStr 日期字符串，格式为yyyyMMdd
     * @return 下一个流水号
     */
    int getNextSerialNumber(String dateStr);
    
    /**
     * 批量获取流水号
     * 
     * @param dateStr 日期字符串，格式为yyyyMMdd
     * @param count 需要的流水号数量
     * @return 起始流水号
     */
    int getBatchSerialNumber(String dateStr, int count);
    
    /**
     * 生成完整的成果编号
     * 
     * @param dateStr 日期字符串，格式为yyyyMMdd
     * @param serialNumber 流水号
     * @return 成果编号，格式为PR+yyyyMMdd+4位流水号
     */
    String generateResultCode(String dateStr, int serialNumber);
}
