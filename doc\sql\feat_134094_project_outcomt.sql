create table jixiaodb.tb_project_result
(
    id                     bigint unsigned auto_increment comment '主键ID'
        primary key,
    business_type_id       bigint unsigned                               not null comment '业务类型ID，关联业务类型管理表主键',
    result_code            varchar(50)                                   not null comment '成果编号',
    result_type            varchar(2)          default ''                null comment '成果类型：1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它',
    project_task_name      varchar(100)                                  not null comment '项目/任务名称，限制30字符',
    priority_level         varchar(2)                                    not null comment '优先级：P1/P2/P3',
    status                 varchar(2)                                    not null comment '状态：1未开始、2进行中、3已完成、4已取消',
    milestone_requirements datetime                                      null comment '完成评审时间',
    milestone_development  datetime                                      null comment '完成开发时间',
    milestone_test         datetime                                      null comment '完成测试验收时间',
    milestone_online       datetime                                      null comment '完成上线时间',
    requirements_progress  decimal(5, 2)                                 null comment '需求评审进度百分比',
    development_progress   decimal(5, 2)                                 null comment '开发进度百分比',
    test_progress          decimal(5, 2)                                 null comment '测试验收进度百分比',
    dev_teams              varchar(1000)       default ''                null comment '开发组，多个用逗号分隔',
    test_teams             varchar(1000)       default ''                null comment '测试组，多个用逗号分隔',
    product_managers       varchar(1000)       default ''                null comment '产品经理(需求创建人)，多个用逗号分隔',
    dev_manpower           int                 default 0                 null comment '开发投入人力（人）',
    test_manpower          int                 default 0                 null comment '测试投入人力（人）',
    dev_workload           decimal(10, 2)      default 0.00              null comment '开发工作量（人日）',
    test_workload          decimal(10, 2)      default 0.00              null comment '测试工作量（人日）',
    requirement_background text                                          null comment '需求背景',
    project_managers       varchar(500)        default ''                not null comment '负责项目经理，多个用逗号分隔',
    completion_time        datetime                                      null comment '完成时间，仅状态为已完成时可填写',
    archive_flag           tinyint(1) unsigned default 0                 not null comment '归档标志：0未归档、1已归档',
    created_by             varchar(30)         default ''                not null comment '创建人',
    created_time           datetime            default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by             varchar(30)         default ''                not null comment '更新人',
    updated_time           datetime            default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag               tinyint(4) unsigned default 0                 null comment '删除标志（0代表存在 2代表删除）'
)
    comment '项目管理主表' charset = utf8mb4;

-- 修改菜单目录
update sys_menu set path = 'ProjectResult'
                  ,component = 'project/projectResult/index'
                  ,perms ='system:projectResult:list'
where menu_name = '项目成果管理';

-- 字典：项目成果管理-优先级
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955516844139921411, '项目成果管理-优先级', 'project_outcome_priority_level', '0', 'admin', '2025-08-13 14:28:42', 'admin', '2025-08-13 14:28:42', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517002273570818, 0, 'P1', 'P1', 'project_outcome_priority_level', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:29:20', 'admin', '2025-08-13 14:29:20', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517051598585858, 1, 'P2', 'P2', 'project_outcome_priority_level', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:29:32', 'admin', '2025-08-13 14:29:32', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517089519288322, 2, 'P3', 'P3', 'project_outcome_priority_level', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:29:41', 'admin', '2025-08-13 14:29:41', NULL);

-- 字典：项目成果管理-状态
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517894083264514, '项目成果管理-状态', 'project_outcome_status', '0', 'admin', '2025-08-13 14:32:52', 'admin', '2025-08-13 14:32:52', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517956007968769, 1, '未开始', '1', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:07', 'admin', '2025-08-13 14:33:28', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955518022470909953, 2, '进行中', '2', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:23', 'admin', '2025-08-13 14:33:23', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955518099029540866, 3, '已完成', '3', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:41', 'admin', '2025-08-13 14:33:41', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955518140494430209, 4, '已取消', '4', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:51', 'admin', '2025-08-13 14:33:51', NULL);

-- 新增项目管理组
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955824073636327426, 15, '项目管理组', '112', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-08-14 10:49:31', 'admin', '2025-08-14 10:49:31', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955824209645023234, 6, '项目管理组', '112', 'project_outcome_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-08-14 10:50:04', 'admin', '2025-08-14 10:50:04', NULL);


create table tb_business_type
(
    id                      bigint                                                not null
        primary key,
    business_type_name      varchar(128)                                          null comment '业务类型名称',
    business_category_major varchar(2) charset utf8mb4  default ''                null comment '所属业务大类：1国内、2海外',
    business_category_minor varchar(2) charset utf8mb4  default ''                null comment '所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它',
    sort                    int                                                   null comment '排序',
    business_manager        varchar(30)                                           null comment '业务负责人',
    created_by              varchar(30) charset utf8mb4 default ''                null comment '创建人',
    created_time            datetime                    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by              varchar(30) charset utf8mb4 default ''                null comment '更新人',
    updated_time            datetime                    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag                tinyint(4) unsigned         default 0                 null comment '删除标志（0代表存在 2代表删除）'
)
    comment '业务类型表';

