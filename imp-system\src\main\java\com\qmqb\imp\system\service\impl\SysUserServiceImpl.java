package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.helper.DataBaseHelper;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.StreamUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.SysPost;
import com.qmqb.imp.system.domain.SysUserPost;
import com.qmqb.imp.system.domain.SysUserRole;
import com.qmqb.imp.system.domain.WorkStat;
import com.qmqb.imp.system.domain.vo.SysUserExportVo;
import com.qmqb.imp.system.mapper.*;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.DeptCacheService;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * 用户 业务层处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService {

    private final SysUserMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;
    private final WorkStatMapper workStatMapper;
    private final DeptCacheService deptCacheService;

    @Override
    public TableDataInfo<SysUser> selectPageUserList(SysUser user, PageQuery pageQuery) {
        Page<SysUser> page = baseMapper.selectPageUserList(pageQuery.build(), this.buildQueryWrapper(user));
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DS(DataSource.GITLAB)
    public List<SysUser> selectUserList(SysUser user) {
        QueryWrapper<SysUser> qw = new QueryWrapper<SysUser>()
            .ne("u.user_id", UserConstants.ADMIN_ID)
            .ne("u.user_id", UserConstants.JSZX_ADMIN_ID)
            .eq("u.del_flag", UserConstants.USER_NORMAL)
            .in("u.dept_id", user.getDeptId());
        return this.baseMapper.selectUserVoList(qw);
    }

    @Override
    @DS(DataSource.GITLAB)
    public List<SysUser> selectAllUser() {
        QueryWrapper<SysUser> qw = new QueryWrapper<SysUser>()
            .ne("u.user_id", UserConstants.ADMIN_ID)
            .ne("u.user_id", UserConstants.JSZX_ADMIN_ID)
            .eq("u.del_flag", UserConstants.USER_NORMAL);
        List<SysUser> sysUsers = this.baseMapper.selectUserVoList(qw);
        return sysUsers;
    }

    @Override
    @DS(DataSource.GITLAB)
    public List<SysUser> selectAllUser2() {
        QueryWrapper<SysUser> qw = new QueryWrapper<SysUser>()
            .eq("u.del_flag", UserConstants.USER_NORMAL);
        List<SysUser> sysUsers = this.baseMapper.selectUserVoList(qw);
        return sysUsers;
    }

    private Wrapper<SysUser> buildQueryWrapper(SysUser user) {
        Map<String, Object> params = user.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
            .eq(ObjectUtil.isNotNull(user.getUserId()), "u.user_id", user.getUserId())
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber())
            .between(params.get("beginTime") != null && params.get("endTime") != null,
                "u.create_time", params.get("beginTime"), params.get("endTime"))
            .and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                List<SysDept> deptList = deptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                    .select(SysDept::getDeptId)
                    .apply(DataBaseHelper.findInSet(user.getDeptId(), "ancestors")));
                List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                ids.add(user.getDeptId());
                w.in("u.dept_id", ids);
            });
        return wrapper;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUser> selectAllocatedList(SysUser user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
            .eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId())
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber());
        Page<SysUser> page = baseMapper.selectAllocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUser> selectUnallocatedList(SysUser user, PageQuery pageQuery) {
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
            .and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id"))
            .notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds)
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber());
        Page<SysUser> page = baseMapper.selectUnallocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return baseMapper.selectUserByUserName(userName);
    }

    @Override
    public SysUser selectUserByNickName(String nickName) {
        return baseMapper.selectUserByNickName(nickName);
    }

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByPhonenumber(String phonenumber) {
        return baseMapper.selectUserByPhonenumber(phonenumber);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return baseMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysRole::getRoleName);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysPost::getPostName);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getUserName, user.getUserName())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getPhonenumber, user.getPhonenumber())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getEmail, user.getEmail())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (ObjectUtil.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    @Override
    public void checkUserChangeDept(SysUser user) {
        //校验是否允许加入新员工组
        this.checkNewMemDeptAllowed(user);
        SysDept oldDept = user.getDept();
        //更换部门的情况
        if (!Objects.isNull(oldDept) && !Objects.equals(user.getDeptId(), oldDept.getDeptId())) {
            //判断用户创建月份和更换新员工组不能在同个月
            if (DateUtil.isSameMonth(user.getCreateTime(), DateUtil.date())
                && Objects.equals(oldDept.getDeptId(), UserConstants.NEW_MEM_DEPT_ID)) {
                throw new ServiceException("部门更换和用户创建不能在同一个月");
            }
        }
    }

    @Override
    public void checkNewMemDeptAllowed(SysUser user) {
        //取创建用户日期为入职时间
        int day = DateUtil.thisDayOfMonth();
        if (!Objects.isNull(user.getCreateTime())) {
            day = DateUtil.dayOfMonth(user.getCreateTime());
        }
        //每月的15日前且加入的是新员工组，则不允许加入新员工组
        if (day < CommConstants.CommonVal.FIFTEEN && Objects.equals(user.getDeptId(), UserConstants.NEW_MEM_DEPT_ID)) {
            throw new ServiceException("每月15日前入职无需加入新员工组");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!LoginHelper.isAdmin()) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = this.selectUserList(user);
            if (CollUtil.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = baseMapper.insert(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        user.setCreateBy(user.getUserName());
        user.setUpdateBy(user.getUserName());
        return baseMapper.insert(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        // 新增用户与岗位管理
        insertUserPost(user);
        // 更新历史工作统计为新部门
        SysDept oldDept = user.getDept();
        if (ObjectUtil.isNotNull(oldDept) && !Objects.equals(user.getDeptId(), oldDept.getDeptId())) {
            updateWorkStat(user.getDeptId(), user.getNickName(), oldDept.getDeptId(), user.getCreateTime());
        }
        return baseMapper.updateById(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>()
            .eq(SysUserRole::getUserId, userId));
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return baseMapper.updateById(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return baseMapper.updateById(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getAvatar, avatar)
                .eq(SysUser::getUserName, userName)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return baseMapper.updateById(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getPassword, password)
                .eq(SysUser::getUserName, userName));
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (ArrayUtil.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.insertBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        return baseMapper.deleteById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        List<Long> ids = Arrays.asList(userIds);
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, ids));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getUserId, ids));
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    public List<SysUserExportVo> listExportVoByDeptCodesAndRoleKeys(List<String> deptCodes, List<String> roleKeys) {
        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.eq("u.status", UserConstants.USER_NORMAL);
        wrapper.eq("u.del_flag", UserConstants.NORMAL);
        wrapper.in("d.dept_code", deptCodes);
        wrapper.in("r.role_key", roleKeys);

        //添加join_time条件到尚未到达第11个工作日的用户
        //计算11个工作日前的日期
        LocalDate currentDate = LocalDate.now();
        LocalDate elevenDaysAgo = HolidayUtil.minusWorkDay(currentDate, 11);
        Date elevenWorkDaysAgo = Date.from(elevenDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant());
        //仅包括在计算日期之前加入_time的用户或没有Join_Time的用户
        wrapper.and(w -> w.isNull("u.join_time").or().le("u.join_time", elevenWorkDaysAgo));

        List<SysUser> list = baseMapper.selectUserVoList(wrapper);
        List<SysUserExportVo> listVo = BeanUtil.copyToList(list, SysUserExportVo.class);

        for (int i = 0; i < list.size(); i++) {
            SysDept dept = list.get(i).getDept();
            SysUserExportVo vo = listVo.get(i);
            if (ObjectUtil.isNotEmpty(dept)) {
                vo.setDeptName(dept.getDeptName());
                vo.setLeader(dept.getLeader());
            }
        }
        return listVo;
    }

    /**
     * 通过昵称查询用户
     *
     * @param nickNames
     * @return
     */
    @Override
    public List<SysUser> selectUserByNickNames(Collection<String> nickNames) {
        return baseMapper.selectUserByNickNames(nickNames);
    }

    /**
     * 通过id列表查询用户
     *
     * @param ids
     * @return
     */
    @Override
    public List<SysUser> selectUserByIds(Collection<String> ids) {
        return baseMapper.selectUserByIds(ids);
    }

    /**
     * 通过部门ID查询用户
     *
     * @param deptId 部门ID
     * @return
     */
    @Override
    public List<SysUser> selectUserByDeptId(Long deptId) {
        QueryWrapper<SysUser> qw = new QueryWrapper<SysUser>()
            .ne("u.user_id", UserConstants.ADMIN_ID)
            .ne("u.user_id", UserConstants.JSZX_ADMIN_ID)
            .eq("u.del_flag", UserConstants.USER_NORMAL)
            .eq("d.dept_id", deptId);
        return this.baseMapper.selectUserVoList(qw);
    }

    @Override
    public List<String> listDepartByUserNames(List<String> userNames) {
        return baseMapper.listDepartByUserNames(userNames);
    }

    /**
     * 更新当年的工作统计部门信息
     *
     * @param newDeptId
     * @param username
     * @param oldDeptId
     */
    private void updateWorkStat(Long newDeptId, String username, Long oldDeptId, Date createTime) {
        //新部门名称
        String newDeptName = deptCacheService.getDeptNameById(newDeptId);
        //根据旧部门信息查询当年历史工作统计
        int thisYear = DateUtil.thisYear();
        int createMonth = DateUtil.month(createTime) + 1;
        LambdaQueryWrapper<WorkStat> wrapper = Wrappers.lambdaQuery(WorkStat.class)
            .eq(WorkStat::getWorkGroupId, oldDeptId)
            .eq(WorkStat::getWorkUsername, username)
            .eq(WorkStat::getWorkYear, thisYear);
        //新员工组换组，排除更新入职当月的数据
        if (ObjectUtil.equals(oldDeptId, UserConstants.NEW_MEM_DEPT_ID)) {
            wrapper.ne(WorkStat::getWorkMonth, createMonth);
        }
        List<WorkStat> workStats = workStatMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(workStats)) {
            //更新历史工作统计为新部门
            workStats.forEach(workStat -> {
                workStat.setWorkGroupId(newDeptId);
                workStat.setWorkGroup(newDeptName);
            });
            workStatMapper.updateBatchById(workStats);
        }
    }

    @Override
    public Integer selectUserCountByRoleName(String roleName, Date endTime) {
        return baseMapper.selectUserCountByRoleName(roleName, endTime);
    }

    @Override
    public List<SysUserExportVo> listExportVoByztUserNames(List<String> ztUsers) {
        return baseMapper.listExportVoByztUserNames(ztUsers);
    }

    @Override
    public List<SysUserExportVo> listExportVoByNickNames(Set<String> leadNames) {
        return baseMapper.listExportVoByNickNames(leadNames);
    }

    @Override
    public List<SysUser> listNewEmployee(LocalDate startTime, LocalDate endTime) {
        QueryWrapper<SysUser> qw = new QueryWrapper<SysUser>()
            .between("u.join_time", startTime, endTime)
            .eq("u.status", 0);
        return this.baseMapper.selectUserVoList(qw);
    }

    @Override
    public List<SysUser> listAllUserInGroup() {
        //获取当前用户的组
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("当前用户不存在");
        }
        //根据组获取所有用户
        List<SysUser> userList = selectUserByDeptId(currentUser.getDeptId());
        return userList;
    }

    @Override
    public List<SysUser> listByRoleId(Long roleId) {
        return baseMapper.listByRoleId(roleId);
    }

    @Override
    public List<SysUser> selectByZtUserNames(List<String> ztUserNames) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysUser>()
            .in(SysUser::getZtUserName,ztUserNames));
    }

}
