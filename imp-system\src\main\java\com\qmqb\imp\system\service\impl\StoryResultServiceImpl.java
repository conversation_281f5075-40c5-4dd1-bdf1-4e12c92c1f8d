package com.qmqb.imp.system.service.impl;

import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.bo.StoryResultBo;
import com.qmqb.imp.system.domain.vo.StoryResultVo;
import com.qmqb.imp.system.mapper.StoryResultMapper;
import com.qmqb.imp.system.service.IStoryResultService;

import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 需求成果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@RequiredArgsConstructor
@Service
public class StoryResultServiceImpl implements IStoryResultService {

    private final StoryResultMapper baseMapper;

    private final ISysUserService sysUserService;


    /**
     * 查询需求成果列表
     */
    @Override
    public TableDataInfo<StoryResultVo> queryPageList(StoryResultBo request, PageQuery pageQuery) {

        if (StringUtils.isNotBlank(request.getOrderByField())) {
            request.setOrderByField(StringUtils.toUnderScoreCase(request.getOrderByField()));
        }
        if (request.getBeginDate() != null) {
            request.setBeginDate(DateUtils.dateToStart(request.getBeginDate()));
        }
        if (request.getEndDate() != null) {
            request.setEndDate(DateUtils.dateToEnd(request.getEndDate()));
        }
        Page<StoryResultVo> result = baseMapper.pageList(pageQuery.build(),request);

        Map<String, String> userMap = sysUserService.selectAllUser().stream().collect(Collectors.toMap(SysUser::getZtUserName, SysUser::getNickName));

        result.getRecords().stream().forEach(item -> {
            String reviewer = Arrays.stream(item.getReviewedBy().split(","))
                .map(account -> userMap.getOrDefault(account.trim(), account.trim()))
                .collect(Collectors.joining(","));
            item.setReviewer(reviewer);
        });

        return TableDataInfo.build(result);
    }

    /**
     * 根据成果ID批量清空需求的成果关联信息
     */
    @Override
    public Boolean clearResultInfoByResultIds(Collection<Long> resultIds) {
        if (resultIds == null || resultIds.isEmpty()) {
            return true;
        }
        return baseMapper.clearResultInfoByResultIds(resultIds) >= 0;
    }


}
