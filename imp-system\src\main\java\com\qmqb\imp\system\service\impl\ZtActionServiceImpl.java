package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.vo.ActionVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.bo.ZtActionBo;
import com.qmqb.imp.system.domain.vo.ZtActionVo;
import com.qmqb.imp.system.domain.ZtAction;
import com.qmqb.imp.system.mapper.ZtActionMapper;
import com.qmqb.imp.system.service.IZtActionService;

import java.util.*;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@RequiredArgsConstructor
@Service
public class ZtActionServiceImpl implements IZtActionService {

    private final ZtActionMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public ZtActionVo queryById(Integer id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public TableDataInfo<ZtActionVo> queryPageList(ZtActionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZtAction> lqw = buildQueryWrapper(bo);
        Page<ZtActionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<ZtActionVo> queryList(ZtActionBo bo) {
        LambdaQueryWrapper<ZtAction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZtAction> buildQueryWrapper(ZtActionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZtAction> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getObjectType()), ZtAction::getObjectType, bo.getObjectType());
        lqw.eq(bo.getObjectId() != null, ZtAction::getObjectId, bo.getObjectId());
        lqw.in(CollUtil.isNotEmpty(bo.getObjectIds()), ZtAction::getObjectId, bo.getObjectIds());
        lqw.eq(StringUtils.isNotBlank(bo.getProduct()), ZtAction::getProduct, bo.getProduct());
        lqw.eq(bo.getProject() != null, ZtAction::getProject, bo.getProject());
        lqw.eq(bo.getExecution() != null, ZtAction::getExecution, bo.getExecution());
        lqw.eq(StringUtils.isNotBlank(bo.getActor()), ZtAction::getActor, bo.getActor());
        lqw.eq(StringUtils.isNotBlank(bo.getAction()), ZtAction::getAction, bo.getAction());
        lqw.eq(bo.getDate() != null, ZtAction::getDate, bo.getDate());
        lqw.eq(StringUtils.isNotBlank(bo.getComment()), ZtAction::getComment, bo.getComment());
        lqw.eq(StringUtils.isNotBlank(bo.getExtra()), ZtAction::getExtra, bo.getExtra());
        lqw.eq(StringUtils.isNotBlank(bo.getRead()), ZtAction::getRead, bo.getRead());
        lqw.eq(bo.getEfforted() != null, ZtAction::getEfforted, bo.getEfforted());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(ZtActionBo bo) {
        ZtAction add = BeanUtil.toBean(bo, ZtAction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(ZtActionBo bo) {
        ZtAction update = BeanUtil.toBean(bo, ZtAction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZtAction entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 通过 objectID列表 与action操作列表 查询禅道操作记录
     */
    @Override
    public List<ActionVO> queryByObjectIdAndAction(List<Integer> objectIdList, List<String> actionList) {
        return this.baseMapper.queryByObjectIdsAndAction(objectIdList, actionList);
    }

    @Override
    public ZtActionVo queryByObjectIdAndAction(Integer objectId, String action) {
        return this.baseMapper.queryByObjectIdAndAction(objectId, action);
    }

    @Override
    public ActionVO queryByObjectIdAndActor(Integer objectId, String actor, Date endTime,String objectType) {
        List<ZtAction> ztActions = this.baseMapper.selectList(Wrappers.lambdaQuery(ZtAction.class)
            .eq(ZtAction::getActor, actor)
            .in(ZtAction::getObjectId, objectId)
            .eq(ZtAction::getObjectType, objectType)
            .between(ZtAction::getDate, endTime, DateUtil.date()));
        return BeanUtil.toBean(ztActions.isEmpty() ? null : ztActions.get(0), ActionVO.class);
    }

    /**
     * 获取任务的所有动作记录，按照时间排序
     */
    @Override
    public List<ZtAction> getTaskActions(Integer taskId) {
        return baseMapper.getTaskActions(taskId);
    }

    @Override
    public List<ZtAction> getActionsByObjIdAndType(Integer objId, String action) {
        return this.baseMapper.selectList(Wrappers.lambdaQuery(ZtAction.class)
            .in(ZtAction::getObjectId, objId)
            .eq(ZtAction::getAction, action)
            .orderByDesc(ZtAction::getDate));
    }

    @Override
    public ZtAction getReleaseActionById(Integer releaseId) {
        // 查询发布操作的时间
        List<ZtAction> publishActions = baseMapper.selectList(
            new LambdaQueryWrapper<ZtAction>()
                .eq(ZtAction::getObjectType, "release")
                .eq(ZtAction::getObjectId, releaseId)
                .eq(ZtAction::getAction, "published")
                .orderByDesc(ZtAction::getDate)
                .last("LIMIT 1")
        );

        if (CollUtil.isNotEmpty(publishActions)) {
            return publishActions.get(0);
        }
        return null;
    }

    @Override
    public Map<Integer, ZtAction> getReleaseActionsByIds(List<Integer> releaseIds) {
        if (CollUtil.isEmpty(releaseIds)) {
            return new HashMap<>(16);
        }

        // 批量查询发布操作记录
        List<ZtAction> publishActions = baseMapper.getReleaseActionsByIds(releaseIds);

        // 转换为Map，key为releaseId，value为ZtAction
        // 如果同一个release有多个published操作，取最新的一个（SQL已按date DESC排序）
        Map<Integer, ZtAction> actionMap = new HashMap<>(16);
        for (ZtAction action : publishActions) {
            if (!actionMap.containsKey(action.getObjectId())) {
                actionMap.put(action.getObjectId(), action);
            }
        }

        return actionMap;
    }

    @Override
    public List<ZtAction> getByObjIdAndTypeAndAction(Integer objectId, String type, String action) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<ZtAction>()
            .eq(ZtAction::getObjectId, objectId)
            .eq(ZtAction::getObjectType, type)
            .eq(ZtAction::getAction, action));
    }

}
