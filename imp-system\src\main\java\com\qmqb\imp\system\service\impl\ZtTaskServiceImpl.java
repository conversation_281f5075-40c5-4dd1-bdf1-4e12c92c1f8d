package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.tool.util.StringUtil;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.dto.TaskInfoQueryDTO;
import com.qmqb.imp.common.core.domain.dto.TaskQueryDTO;
import com.qmqb.imp.common.core.domain.dto.TaskStatisticsDTO;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.ZtAction;
import com.qmqb.imp.system.domain.ZtTask;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.bo.ZtTaskBo;
import com.qmqb.imp.system.domain.vo.*;
import com.qmqb.imp.system.mapper.ZtTaskMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.helper.DataUtil;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 禅道任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ZtTaskServiceImpl implements IZtTaskService {

    private final ZtTaskMapper baseMapper;
    private final ISysUserService sysUserService;
    private final IZtActionService ztActionService;
    private final IZtProjectService ztProjectService;
    private final IZtActionService iZtActionService;

    private final IWarnConfigService warnConfigService;

    /**
     * 查询禅道任务
     */
    @Override
    public ZtTaskVo queryById(Integer id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询禅道任务列表
     */
    @Override
    public TableDataInfo<ZtTaskVo> queryPageList(ZtTaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZtTask> lqw = buildQueryWrapper(bo);
        Page<ZtTaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询禅道任务列表
     */
    @Override
    public List<ZtTaskVo> queryList(ZtTaskBo bo) {
        LambdaQueryWrapper<ZtTask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZtTask> buildQueryWrapper(ZtTaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZtTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProject() != null, ZtTask::getProject, bo.getProject());
        lqw.eq(bo.getParent() != null, ZtTask::getParent, bo.getParent());
        lqw.eq(bo.getExecution() != null, ZtTask::getExecution, bo.getExecution());
        lqw.eq(bo.getModule() != null, ZtTask::getModule, bo.getModule());
        lqw.eq(bo.getDesign() != null, ZtTask::getDesign, bo.getDesign());
        lqw.eq(bo.getStory() != null, ZtTask::getStory, bo.getStory());
        lqw.eq(bo.getStoryVersion() != null, ZtTask::getStoryVersion, bo.getStoryVersion());
        lqw.eq(bo.getDesignVersion() != null, ZtTask::getDesignVersion, bo.getDesignVersion());
        lqw.eq(bo.getFromBug() != null, ZtTask::getFromBug, bo.getFromBug());
        lqw.eq(bo.getFeedback() != null, ZtTask::getFeedback, bo.getFeedback());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ZtTask::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ZtTask::getType, bo.getType());
        lqw.eq(bo.getPri() != null, ZtTask::getPri, bo.getPri());
        lqw.eq(StringUtils.isNotBlank(bo.getEstimate()), ZtTask::getEstimate, bo.getEstimate());
        lqw.eq(StringUtils.isNotBlank(bo.getConsumed()), ZtTask::getConsumed, bo.getConsumed());
        lqw.eq(StringUtils.isNotBlank(bo.getLeft()), ZtTask::getLeft, bo.getLeft());
        lqw.eq(bo.getDeadline() != null, ZtTask::getDeadline, bo.getDeadline());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ZtTask::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSubStatus()), ZtTask::getSubStatus, bo.getSubStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getColor()), ZtTask::getColor, bo.getColor());
        lqw.eq(StringUtils.isNotBlank(bo.getMailto()), ZtTask::getMailto, bo.getMailto());
        lqw.eq(StringUtils.isNotBlank(bo.getDesc()), ZtTask::getDesc, bo.getDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenedBy()), ZtTask::getOpenedBy, bo.getOpenedBy());
        lqw.eq(bo.getOpenedDate() != null, ZtTask::getOpenedDate, bo.getOpenedDate());
        lqw.eq(StringUtils.isNotBlank(bo.getAssignedTo()), ZtTask::getAssignedTo, bo.getAssignedTo());
        lqw.eq(bo.getAssignedDate() != null, ZtTask::getAssignedDate, bo.getAssignedDate());
        lqw.eq(bo.getEstStarted() != null, ZtTask::getEstStarted, bo.getEstStarted());
        lqw.eq(bo.getRealStarted() != null, ZtTask::getRealStarted, bo.getRealStarted());
        lqw.eq(StringUtils.isNotBlank(bo.getFinishedBy()), ZtTask::getFinishedBy, bo.getFinishedBy());
        lqw.eq(bo.getFinishedDate() != null, ZtTask::getFinishedDate, bo.getFinishedDate());
        lqw.eq(StringUtils.isNotBlank(bo.getFinishedList()), ZtTask::getFinishedList, bo.getFinishedList());
        lqw.eq(StringUtils.isNotBlank(bo.getCanceledBy()), ZtTask::getCanceledBy, bo.getCanceledBy());
        lqw.eq(bo.getCanceledDate() != null, ZtTask::getCanceledDate, bo.getCanceledDate());
        lqw.eq(StringUtils.isNotBlank(bo.getClosedBy()), ZtTask::getClosedBy, bo.getClosedBy());
        lqw.eq(bo.getClosedDate() != null, ZtTask::getClosedDate, bo.getClosedDate());
        lqw.eq(bo.getRealDuration() != null, ZtTask::getRealDuration, bo.getRealDuration());
        lqw.eq(bo.getPlanDuration() != null, ZtTask::getPlanDuration, bo.getPlanDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getClosedReason()), ZtTask::getClosedReason, bo.getClosedReason());
        lqw.eq(StringUtils.isNotBlank(bo.getLastEditedBy()), ZtTask::getLastEditedBy, bo.getLastEditedBy());
        lqw.eq(bo.getLastEditedDate() != null, ZtTask::getLastEditedDate, bo.getLastEditedDate());
        lqw.eq(bo.getActivatedDate() != null, ZtTask::getActivatedDate, bo.getActivatedDate());
        lqw.eq(StringUtils.isNotBlank(bo.getDeleted()), ZtTask::getDeleted, bo.getDeleted());
        return lqw;
    }

    /**
     * 新增禅道任务
     */
    @Override
    public Boolean insertByBo(ZtTaskBo bo) {
        ZtTask add = BeanUtil.toBean(bo, ZtTask.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改禅道任务
     */
    @Override
    public Boolean updateByBo(ZtTaskBo bo) {
        ZtTask update = BeanUtil.toBean(bo, ZtTask.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZtTask entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除禅道任务
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取禅道任务查询分页
     *
     * @param request
     * @return
     */
    @Override
    public Page<TaskVO> page(TaskQueryDTO request) {
        Page<TaskVO> page = new Page<>(request.getPageNo(), request.getPageSize());
        request.setCompletedBy(Objects.nonNull(request.getCompletedBy()) ? request.getCompletedBy().trim() : null);
        request.setTaskStarter(Objects.nonNull(request.getTaskStarter()) ? request.getTaskStarter().trim() : null);
        List<SysUser> sysUsers = sysUserService.selectAllUser();
        if (ObjectUtil.isNotNull(request.getGroupId()) && !UserConstants.JSZX_DEPT_ID.equals(request.getGroupId())) {
            Map<Long, List<SysUser>> sysUserMap = sysUsers.stream().collect(Collectors.groupingBy(e -> e.getDept().getDeptId()));
            List<SysUser> groupUsers = sysUserMap.get(request.getGroupId());
            if (CollectionUtil.isEmpty(groupUsers)) {
                return page;
            }
            List<String> usernameList = groupUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
            request.setUserNameList(usernameList);
        } else {
            List<String> usernameList = sysUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
            request.setUserNameList(usernameList);
        }
        List<TaskVO> retList = this.baseMapper.getListsByPage(page, request);
        //查询父任务的标题(parent大于0的就是有父标题)
        Set<Integer> parents = retList.stream().map(TaskVO::getParent).collect(Collectors.toSet());
        if (parents.size() > 0) {
            List<ZtTaskVo> parentsList = baseMapper.listByIds(parents);
            Map<Integer, ZtTaskVo> parentsMap = parentsList.stream().collect(Collectors.toMap(ZtTaskVo::getId, Function.identity()));
            retList.forEach(tmp -> {
                if (tmp.getParent() > 0 && Objects.nonNull(parentsMap.get(tmp.getParent()))) {
                    tmp.setName(parentsMap.get(tmp.getParent()).getName());
                } else {
                    tmp.setSonName(null);
                }
            });
        }

        Map<String, String> userNameMap = sysUsers.stream().filter(user -> StringUtils.isNotEmpty(user.getZtUserName())).collect(Collectors.toMap(SysUser::getZtUserName, SysUser::getNickName, (k1, k2) -> k1));
        List<Integer> executionIds = retList.stream().map(TaskVO::getExecution).collect(Collectors.toList());
        Map<Integer, String> projectNameMap = ztProjectService.listByIds(executionIds).stream().collect(Collectors.toMap(ZtProjectVo::getId, ZtProjectVo::getName));
        retList.forEach(ret -> {
            ret.setClosedby(userNameMap.get(ret.getClosedby()));
            ret.setPName(projectNameMap.get(ret.getExecution()));
        });
        return page.setRecords(retList);
    }

    /**
     * 根据完成时间查询查询已完成任务
     *
     * @param fromDate
     * @param toDate
     * @return
     */
    @Override
    public List<ZtTask> listDoneByFinishedDate(DateTime fromDate, DateTime toDate) {
        return baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class).gt(ZtTask::getParent, -1).eq(ZtTask::getIsParent,false).in(ZtTask::getStatus, Arrays.asList("done", "closed")).between(ZtTask::getFinishedDate, fromDate, toDate));
    }

    /**
     * 根据开始时间查询查询进行中任务
     *
     * @param fromDate
     * @param toDate
     * @return
     */
    @Override
    public List<ZtTask> listDoingByRealStarted(DateTime fromDate, DateTime toDate) {
        return baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class).gt(ZtTask::getParent, -1).eq(ZtTask::getIsParent,false).eq(ZtTask::getStatus, "doing").between(ZtTask::getRealStarted, fromDate, toDate));
    }

    /**
     * 根据状态和开启时间任务
     *
     * @param status
     * @param fromDate
     * @param toDate
     * @return
     */
    @Override
    public List<ZtTask> listByStatusAndOpenedDate(List<String> status, DateTime fromDate, DateTime toDate) {
        return baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class).in(ZtTask::getStatus, status).between(ZtTask::getOpenedDate, fromDate, toDate));
    }

    /**
     * 根据用户拼音名小写列表以及开始和结束时间获取用户的任务个数列表
     *
     * @param userNameList
     * @param beginTime
     * @param endTime
     * @return
     */
    @Override
    @DS(DataSource.ZENTAO)
    public List<ZtTaskCountVO> getTaskListByTimeAndUser(List<String> userNameList, Date beginTime, Date endTime) {
        return this.baseMapper.getTaskListByTimeAndUser(userNameList, beginTime, endTime);
    }

    /**
     * 根据月份和组别查询任务列表
     *
     * @param request
     * @return
     * @throws IOException
     */
    @Override
    public TableDataInfo<TaskInfoVO> getTaskListByGroupAndMonth(TaskInfoQueryDTO request) {
        Page<TaskInfoVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        List<SysUser> sysUsers = sysUserService.selectAllUser();
        Date beginTime = request.getDateArr()[0];
        Date endTime = DateUtils.dateToEnd(request.getDateArr()[1]);
        List<String> usernameList;
        if (ObjectUtil.isNull(request.getGroupId()) || UserConstants.JSZX_DEPT_ID.equals(request.getGroupId())) {
            usernameList = sysUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
        } else {
            Map<Long, List<SysUser>> sysUserMap = sysUsers.stream().collect(Collectors.groupingBy(e -> e.getDept().getDeptId()));
            List<SysUser> groupUser = sysUserMap.get(request.getGroupId());
            if (CollectionUtil.isEmpty(groupUser)) {
                return TableDataInfo.build();
            }
            usernameList = groupUser.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
        }
        List<TaskInfoVO> taskInfoVOList = this.baseMapper.getTaskListByGroupAndMonth(usernameList, beginTime, endTime, request.getTaskStatus(), request.getName(), page);
        taskInfoVOList.forEach(taskInfoVO -> {
            taskInfoVO.setUserName(sysUsers.stream().filter(user -> StringUtils.equals(taskInfoVO.getUserName(), user.getZtUserName())).map(SysUser::getNickName).findAny().orElse(null));
            taskInfoVO.setTaskPrimary("P" + taskInfoVO.getTaskPrimary());
            taskInfoVO.setStatus(ZtTaskStatusEnum.getDescByValue(taskInfoVO.getStatus()));
        });
        TableDataInfo<TaskInfoVO> dataInfo = TableDataInfo.build(page);
        dataInfo.setRows(taskInfoVOList);
        return dataInfo;
    }

    /**
     * 查询进行中的任务
     *
     * @param userNameList 用户名列表
     * @param endTime      结束时间
     * @return
     */
    private List<ZtTask> queryDoingTaskByTime(List<String> userNameList, Date endTime) {
        return this.baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class)
            .eq(ZtTask::getStatus, ZtTaskStatusEnum.DOING.getValue())
            .in(ZtTask::getLastEditedBy, userNameList)
            .lt(ZtTask::getLastEditedDate, endTime));
    }

    /**
     * 有任务但迟迟未开始的场景: 根据指派的人, 状态为未开始, 最后编辑时间是几天之前来查询
     *
     * @param userNameList 用户名列表
     * @param endTime      结束时间
     * @return
     */
    private List<ZtTask> queryNotStartTaskByTime(List<String> userNameList, Date endTime) {
        return this.baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class)
            .eq(ZtTask::getStatus, ZtTaskStatusEnum.WAIT.getValue())
            .in(ZtTask::getAssignedTo, userNameList)
            .lt(ZtTask::getAssignedDate, endTime));
    }

    /**
     * 指定时间内没有任务的场景: 根据指派的人, 指派时间几天内没有任务
     *
     * @param userNameList 用户名列表
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return
     */
    private List<ZtTask> queryHasTaskByTime(List<String> userNameList, Date beginTime, Date endTime) {
        return this.baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class)
            .in(ZtTask::getFinishedBy, userNameList)
            .in(ZtTask::getStatus, Arrays.asList(ZtTaskStatusEnum.DONE.getValue(), ZtTaskStatusEnum.CLOSED.getValue()))
            .between(ZtTask::getFinishedDate, beginTime, endTime));
    }

    private List<ZtTask> queryDoingTask(List<String> userNameList) {
        return this.baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class)
            .in(ZtTask::getAssignedTo, userNameList)
            .eq(ZtTask::getStatus, ZtTaskStatusEnum.DOING.getValue()));
    }

    @Override
    public List<SysUserExportVo> dealNoTaskWarnObject(List<SysUserExportVo> sysUserExportVoList, Date beginTime, Date endTime) {

        // 查询没有进行中任务的成员
        List<String> userNameList = sysUserExportVoList.stream().map(SysUserExportVo::getZtUserName).collect(Collectors.toList());
        List<ZtTask> ztTaskList = this.queryDoingTask(userNameList);
        List<String> doingTaskUserNames = ztTaskList.stream().map(ZtTask::getAssignedTo).distinct().collect(Collectors.toList());
        LocalDate currentDate = LocalDate.now();
        List<SysUserExportVo> notDoingTaskUsers = sysUserExportVoList.stream()
            .filter(user -> !doingTaskUserNames.contains(user.getZtUserName()))
            .collect(Collectors.toList());
        notDoingTaskUsers = notDoingTaskUsers.stream().filter(user -> !HolidayUtil.isLeave(DateUtils.dateTime(beginTime), DateUtils.dateTime(endTime), user.getUserId())).collect(Collectors.toList());
        // 指定时间内没有完成任务的成员
        if (CollectionUtil.isNotEmpty(notDoingTaskUsers)) {
            List<String> notDoingTaskUserNames = notDoingTaskUsers.stream().map(SysUserExportVo::getZtUserName).collect(Collectors.toList());
            ztTaskList = this.queryHasTaskByTime(notDoingTaskUserNames, beginTime, endTime);
            List<String> hasDoneTaskUserNames = ztTaskList.stream().map(ZtTask::getFinishedBy).distinct().collect(Collectors.toList());
            return notDoingTaskUsers.stream().filter(user -> !hasDoneTaskUserNames.contains(user.getZtUserName())).collect(Collectors.toList());
        }

        return Lists.newArrayList();
    }

    @Override
    public List<SysUserExportVo> dealDoingTaskWarnObject(List<SysUserExportVo> sysUserExportVoList, Date endTime) {
        List<String> userNameList = sysUserExportVoList.stream().map(SysUserExportVo::getZtUserName).collect(Collectors.toList());
        List<ZtTask> ztTaskList = this.queryDoingTaskByTime(userNameList, endTime);
        ztTaskList = ztTaskList.stream().filter(ztTask -> StringUtil.equals(ztTask.getLastEditedBy(), ztTask.getAssignedTo())).collect(Collectors.toList());
        List<String> warnUserNameList = warnUserNameListFilterAction(ztTaskList, endTime);
        Date nowDate = DateUtils.getNowDate();
        LocalDate currentDate = LocalDate.now();
        return sysUserExportVoList.stream()
            .filter(user -> warnUserNameList.contains(user.getUserName()))
            .filter(user -> !HolidayUtil.isLeave(DateUtils.dateTime(endTime), DateUtils.dateTime(nowDate), user.getUserId()))
            .collect(Collectors.toList());
    }


    /**
     * 禅道升级之后 记录工时不更新 lastEditedDate 需要去读取记录工时的表
     *
     * @param ztTaskList
     * @return
     */
    private List<String> warnUserNameListFilterAction(List<ZtTask> ztTaskList, Date endTime) {
        List<ZtTask> collect = ztTaskList.stream().filter(task ->
                // 规定时间内没记录时间的需要预警
                ObjectUtil.isNull(iZtActionService.queryByObjectIdAndActor(task.getId(), task.getLastEditedBy(), endTime,"task")))
            .collect(Collectors.toList());
        return collect.stream().map(ZtTask::getLastEditedBy).distinct().collect(Collectors.toList());
    }

    /**
     * 任务统计
     *
     * @param request
     * @return
     */
    @Override
    public TableDataInfo<TaskStatisticsVO> statisticsNoDoing(TaskStatisticsDTO request) {
        List<String> roleKeys = RoleKeyEnum.getRoleKeyByWarnObject(WarnObjectEnum.ALL_EXPECT_CTO.getValue());
        List<String> deptCodes = DeptCodeEnum.getCodeByWarnObject(WarnObjectEnum.ALL_EXPECT_CTO.getValue());
        List<SysUserExportVo> sysUser = sysUserService.listExportVoByDeptCodesAndRoleKeys(deptCodes, roleKeys);
        DateTime beginTime = DateTime.now().offset(DateField.DAY_OF_MONTH, -3);
        DateTime endTime = DateTime.now();
        // 时间范围内有进行中任务的人
        List<String> usernames = sysUser.stream().map(SysUserExportVo::getZtUserName).collect(Collectors.toList());
        List<ZtTask> ztTasks = this.baseMapper.queryDoingTaskByStartedTime(usernames, beginTime, endTime);
        Set<String> hasDoingTaskUser = ztTasks.stream().map(ZtTask::getLastEditedBy).collect(Collectors.toSet());
        // 筛选无有进行中任务的人
        List<TaskStatisticsVO> vos = sysUser.stream()
            .filter(e -> !hasDoingTaskUser.contains(e.getZtUserName()))
            .map(e -> TaskStatisticsVO.builder().workGroup(e.getDeptName()).workUsername(e.getNickName()).build())
            .collect(Collectors.toList());
        return TableDataInfo.page(vos, request.getPageNum(), request.getPageSize());
    }


    /**
     * 根据状态查询任务数量
     *
     * @param monthStartTime
     * @param monthEndTime
     * @param ztTaskStatusEnum
     * @param selectTaskCountByStatus
     * @return
     */
    @Override
    public Long selectTaskCountByStatus(Date monthStartTime, Date monthEndTime, ZtTaskStatusEnum ztTaskStatusEnum, List<String> usernameList) {
        Long count = baseMapper.selectTaskCountByStatus(ztTaskStatusEnum.getValue(),monthStartTime,monthEndTime,usernameList);
        return count;
    }

    @Override
    public Page<TaskVO> queryUserTask(WorkDetailBo workDetail) {
        Page<TaskVO> page = new Page<>(workDetail.getPageNo(), workDetail.getPageSize());
        List<SysUser> sysUsers = sysUserService.selectUserByNickNames(Collections.singleton(workDetail.getWorkUsername()));
        if (CollUtil.isEmpty(sysUsers)) {
            throw new ServiceException("该用户不存在");
        }
        workDetail.setZtUserName(sysUsers.get(0).getZtUserName());
        Page<TaskVO> taskVoPage = this.baseMapper.queryUserTask(page, workDetail);
        //设置完成人，开启人，父标题
        List<SysUser> allUsers = sysUserService.selectAllUser();
        Map<String, String> userNameMap = allUsers.stream().filter(user -> StringUtils.isNotEmpty(user.getZtUserName()))
            .collect(Collectors.toMap(SysUser::getZtUserName, SysUser::getNickName, (k1, k2) -> k1));
        Set<Integer> parents = taskVoPage.getRecords().stream().map(TaskVO::getParent).collect(Collectors.toSet());
        if (parents.size() > 0) {
            Map<Integer, ZtTaskVo> parentsMap = baseMapper.listByIds(parents).stream().collect(Collectors.toMap(ZtTaskVo::getId, Function.identity()));
            taskVoPage.getRecords().forEach(tmp -> {
                tmp.setClosedby(userNameMap.getOrDefault(tmp.getClosedby(),tmp.getClosedby()));
                tmp.setFinishedby(userNameMap.getOrDefault(tmp.getFinishedby(),tmp.getFinishedby()));
                tmp.setTaskStarter(userNameMap.getOrDefault(tmp.getTaskStarter(),tmp.getTaskStarter()));
                if (tmp.getParent() > 0 && Objects.nonNull(parentsMap.get(tmp.getParent()))) {
                    tmp.setName(parentsMap.get(tmp.getParent()).getName());
                } else {
                    tmp.setName(tmp.getSonName());
                    tmp.setSonName(null);
                }
            });
        }
        return taskVoPage;
    }

    @DS(DataSource.GITLAB)
    @Override
    public List<TaskVO> doingTaskTimeout() {
        WarnConfigVo usedByWarnCode = warnConfigService.getUsedByWarnCode(WarnCodeEnum.TASK_WARN_DOING_NOT_FINISH_P2.getCode());
        if (Objects.isNull(usedByWarnCode)) {
            throw new ServiceException("未配置任务超时提醒");
        }
        WarnTriggerRuleVo rule = usedByWarnCode.getRule();
        CompareSymbolEnum symbolEnum = CompareSymbolEnum.getByType(rule.getCompareSymbol());
        if (symbolEnum == null || rule.getCompareValue() == null) {
            throw new ServiceException("未配置任务超时提醒规则");
        }
        // 获取未完成的任务列表
        List<TaskVO> tasks = this.baseMapper.doingTaskTimeout(symbolEnum.getSymbol(), rule.getCompareValue());

        long secondsThreshold = rule.getCompareValue().longValue() * 24 * 60 * 60;
        List<TaskVO> result = new ArrayList<>();

        // 遍历每个任务，计算工作时间
        for (TaskVO task : tasks) {
            // 获取任务所有动作记录
            List<ZtAction> actions = iZtActionService.getTaskActions(task.getId());
            if (actions == null || actions.isEmpty()) {
                continue;
            }
            // 计算任务总工作时间
            long totalWorkingSeconds = calculateTaskWorkingTime(actions);
            // 如果总工作时间超过阈值，添加到结果中
            if (totalWorkingSeconds > secondsThreshold) {
                result.add(task);
            }
        }
        return result;
    }

    /**
     * 计算任务工作时间
     *
     * @param actions 任务动作列表
     * @return 总工作时间（秒）
     */
    private long calculateTaskWorkingTime(List<ZtAction> actions) {
        long totalWorkingSeconds = 0;
        Date startTime = null;

        for (int i = 0; i < actions.size(); i++) {
            ZtAction action = actions.get(i);
            String actionType = action.getAction();
            // 记录开始时间
            if (ZtTaskActionEnum.STARTED.getValue().equals(actionType) || ZtTaskActionEnum.RESTARTED.getValue().equals(actionType)) {
                startTime = action.getDate();
            }
            // 计算到暂停的时间
            else if (ZtTaskActionEnum.PAUSED.getValue().equals(actionType) && startTime != null) {
                totalWorkingSeconds += calculateWorkingSecondsBetweenDates(startTime, action.getDate());
                startTime = null;
            }
        }
        // 如果最后一个操作是started或restarted，需要计算到当前时间
        if (startTime != null) {
            totalWorkingSeconds += calculateWorkingSecondsBetweenDates(startTime, new Date());
        }

        return totalWorkingSeconds;
    }

    /**
     * 计算两个日期之间的工作时间（排除节假日）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工作时间（秒）
     */
    private long calculateWorkingSecondsBetweenDates(Date startDate, Date endDate) {
        LocalDateTime start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        // 如果开始时间和结束时间在同一天
        if (DateUtils.isSameDay(startDate, endDate)) {
            String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, startDate);
            if (HolidayUtil.isHoliday(dateStr)) {
                return 0;
            }
            return Duration.between(start, end).getSeconds();
        }
        // 跨天计算工作时间
        long totalWorkingSeconds = 0;

        // 处理第一天（从开始时间到当天结束）
        LocalDate currentDay = start.toLocalDate();
        if (!HolidayUtil.isHoliday(currentDay.toString())) {
            totalWorkingSeconds += Duration.between(start, currentDay.atTime(LocalTime.MAX)).getSeconds();
        }

        // 处理中间的整天
        currentDay = currentDay.plusDays(1);
        while (currentDay.isBefore(end.toLocalDate())) {
            if (!HolidayUtil.isHoliday(currentDay.toString())) {
                totalWorkingSeconds += Duration.ofDays(1).getSeconds();
            }
            currentDay = currentDay.plusDays(1);
        }

        // 处理最后一天（从当天开始到结束时间）
        if (!HolidayUtil.isHoliday(end.toLocalDate().toString())) {
            totalWorkingSeconds += Duration.between(end.toLocalDate().atStartOfDay(), end).getSeconds();
        }

        return totalWorkingSeconds;
    }

    @Override
    public List<ZtTask> listAllDoing() {
        return baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class)
            .gt(ZtTask::getParent, -1)
            .eq(ZtTask::getIsParent, false)
            .eq(ZtTask::getStatus, ZtTaskStatusEnum.DOING.getValue())
            .eq(ZtTask::getDeleted, "0")
            .exists("SELECT 1 FROM zt_project p WHERE p.id = zt_task.execution AND p.deleted = '0'"));
    }

    @Override
    public List<ZtTask> listAllPaused() {
        return baseMapper.selectList(Wrappers.lambdaQuery(ZtTask.class)
            .eq(ZtTask::getStatus, ZtTaskStatusEnum.PAUSE.getValue()));
    }

    @Override
    public List<Long> selectTaskGroup(List<Long> storyIdList, Integer isParent, String type) {
        List<String> devUserNames = baseMapper.selectList(new LambdaQueryWrapper<ZtTask>()
                .in(ZtTask::getStory, storyIdList)
                .eq(ZtTask::getIsParent, isParent)
                .eq(ZtTask::getType, type))
            .stream().map(task -> {
                // 优先取 assignedTo，如果为null或任务已关闭，则取 finishedBy
                if (task.getAssignedTo() != null && !ZtTaskStatusEnum.CLOSED.getValue().equals(task.getStatus())) {
                    return task.getAssignedTo();
                } else if (task.getFinishedBy() != null) {
                    return task.getFinishedBy();
                }
                return null;
            }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<SysUser> userList = sysUserService.selectByZtUserNames(devUserNames);
        return userList.stream().map(SysUser::getDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }
}
