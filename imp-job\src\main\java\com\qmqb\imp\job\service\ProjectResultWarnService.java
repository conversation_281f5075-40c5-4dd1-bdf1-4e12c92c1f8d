package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.ProjectResultMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;

/**
 * 项目成果预警通知定时器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectResultWarnService {

    private final ProjectResultMapper projectResultMapper;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;

    final String milestoneRequirementsStr="milestone_requirements";
    final String requirementsProgressStr="requirements_progress";
    final String devManpowerStr="dev_manpower";
    final String testManpowerStr="test_manpower";
    final String devWorkloadStr="dev_workload";
    final String testWorkloadStr="test_workload";

    @TraceId("项目成果预警通知定时任务")
    @XxlJob("projectResultWarnServiceJobHandler")
    public ReturnT<String> projectResultWarnServiceJobHandler(String param) {
        try {
            //节假日不执行定时任务
            if(HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("开始执行项目成果预警通知定时任务...");
            log.info("开始执行项目成果预警通知定时任务");
            val sw = new StopWatch();
            sw.start();

            // 统计缺失信息的项目成果数量
            List<Map<String, Object>> incompleteProjectResults = getIncompleteProjectResults();

            // 发送通知
            if (CollectionUtil.isNotEmpty(incompleteProjectResults)) {
                int count = incompleteProjectResults.size();
                send(Constants.PROJECT_RESULT_INFO_LACK_TAG, count);
                XxlJobLogger.log("发送项目成果预警通知，缺失信息的项目成果数量：{}", count);
                log.info("发送项目成果预警通知，缺失信息的项目成果数量：{}", count);
            } else {
                log.info("所有项目成果信息均已完善，无需发送通知");
                XxlJobLogger.log("所有项目成果信息均已完善，无需发送通知");
            }

            sw.stop();
            XxlJobLogger.log("项目成果预警通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            log.info("项目成果预警通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("项目成果预警通知定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 获取信息不完整的项目成果列表
     * 统计逻辑：只要这几个项有空值则算作一条数据
     * - 项目里程碑 = 完成评审时间+完成开发时间+完成测试验收时间+完成上线时间
     * - 任务说明/进度 = 需求评审进度+开发进度+测试验收进度
     * - 干系人 = 产品+开发组+测试组
     * - 投入人力 = 开发+测试
     * - 工作量(日) = 开发+测试
     *
     * @return 信息不完整的项目成果列表
     */
    private List<Map<String, Object>> getIncompleteProjectResults() {
        try {
            // 查询所有非"事项支撑"类型的项目成果（成果类型不等于4）
            List<Map<String, Object>> rawResults = projectResultMapper.selectIncompleteProjectResults();
            List<Map<String, Object>> processedResults = new ArrayList<>();

            for (Map<String, Object> result : rawResults) {
                List<String> missingFields = analyzeMissingFields(result);
                if (!missingFields.isEmpty()) {
                    result.put("missing_fields", missingFields);
                    processedResults.add(result);
                }
            }

            return processedResults;
        } catch (Exception e) {
            log.error("查询信息不完整的项目成果异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 分析缺失的字段
     *
     * @param result 项目成果数据
     * @return 缺失的字段列表
     */
    private List<String> analyzeMissingFields(Map<String, Object> result) {
        List<String> missingFields = new ArrayList<>();

        // 检查项目里程碑：完成评审时间+完成开发时间+完成测试验收时间+完成上线时间
        if (result.get(milestoneRequirementsStr) == null ||
            result.get("milestone_development") == null ||
            result.get("milestone_test") == null ||
            result.get("milestone_online") == null) {
            missingFields.add("项目里程碑");
        }

        // 检查任务进度：需求评审进度+开发进度+测试验收进度
        if (result.get(requirementsProgressStr) == null ||
            result.get("development_progress") == null ||
            result.get("test_progress") == null) {
            missingFields.add("任务进度");
        }

        // 检查干系人：产品+开发组+测试组
        String productManagers = (String) result.get("product_managers");
        String devTeams = (String) result.get("dev_teams");
        String testTeams = (String) result.get("test_teams");
        if (StrUtil.isBlank(productManagers) || StrUtil.isBlank(devTeams) || StrUtil.isBlank(testTeams)) {
            missingFields.add("干系人");
        }

        // 检查投入人力：开发+测试
        if (result.get(devManpowerStr) == null || result.get(testManpowerStr) == null) {
            missingFields.add("投入人力");
        }

        // 检查工作量：开发+测试
        if (result.get(devWorkloadStr) == null || result.get(testWorkloadStr) == null) {
            missingFields.add("工作量");
        }

        return missingFields;
    }

    /**
     * 构建详细信息字符串
     *
     * @param incompleteProjectResults 信息不完整的项目成果列表
     * @return 详细信息字符串
     */
    private String buildDetailsString(List<Map<String, Object>> incompleteProjectResults) {
        if (CollectionUtil.isEmpty(incompleteProjectResults)) {
            return "";
        }

        StringBuilder details = new StringBuilder();
        int index = 1;
        for (Map<String, Object> result : incompleteProjectResults) {
            String projectTaskName = (String) result.get("project_task_name");
            String resultCode = (String) result.get("result_code");
            @SuppressWarnings("unchecked")
            List<String> missingFields = (List<String>) result.get("missing_fields");

            details.append(index).append(". ")
                   .append("【").append(resultCode).append("】")
                   .append(projectTaskName)
                   .append(" - 缺失：").append(String.join("、", missingFields))
                   .append("\n");
            index++;
        }

        return details.toString();
    }

    /**
     * 发送预警
     *
     * @param template 模板
     * @param count    缺失信息的项目成果数量
     */
    private void send(String template, int count) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("count", count);
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
                .url(dingTalkConfig.getRobotUrl())
                .msgtype("text")
                .content(content)
                .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
