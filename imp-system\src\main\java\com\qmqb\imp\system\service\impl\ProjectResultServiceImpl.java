package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.ProjectResult;
import com.qmqb.imp.system.domain.bo.ProjectResultBo;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;
import com.qmqb.imp.system.mapper.ProjectResultMapper;
import com.qmqb.imp.system.service.IProjectResultService;
import com.qmqb.imp.system.service.IProjectResultNumberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 项目成果表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProjectResultServiceImpl implements IProjectResultService {

    private final ProjectResultMapper baseMapper;
    private final IProjectResultNumberService projectResultNumberService;

    /**
     * 查询项目成果表
     */
    @Override
    public ProjectResultVo queryById(Long id) {
        ProjectResultVo vo = baseMapper.selectVoByIdWithBusinessType(id);
        return vo;
    }

    /**
     * 查询项目成果表列表
     */
    @Override
    public TableDataInfo<ProjectResultVo> queryPageList(ProjectResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProjectResult> lqw = buildQueryWrapper(bo);

        // 构建分页对象
        Page<ProjectResult> page = pageQuery.build();

        // 处理排序参数
        String orderByColumn = pageQuery.getOrderByColumn();
        String[] orderByColumns = null;
        String[] isAscArray = null;

        if (StringUtils.isNotBlank(orderByColumn)) {
            orderByColumns = orderByColumn.split(",");
            String isAsc = pageQuery.getIsAsc();
            isAscArray = StringUtils.isNotBlank(isAsc) ? isAsc.split(",") : new String[]{"asc"};

            // 转换字段名为数据库字段名
            for (int i = 0; i < orderByColumns.length; i++) {
                orderByColumns[i] = StringUtils.toUnderScoreCase(orderByColumns[i].trim());
            }
        }

        Page<ProjectResultVo> result = baseMapper.selectVoPageWithBusinessType(page, lqw,
                                                                               bo.getBusinessCategoryMajor(),
                                                                               bo.getBusinessCategoryMinor(),
                                                                               orderByColumn,
                                                                               orderByColumns,
                                                                               isAscArray);

        return TableDataInfo.build(result);
    }

    /**
     * 查询项目成果表列表
     */
    @Override
    public List<ProjectResultVo> queryList(ProjectResultBo bo) {
        LambdaQueryWrapper<ProjectResult> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoListWithBusinessType(lqw,
                                                       bo.getBusinessCategoryMajor(),
                                                       bo.getBusinessCategoryMinor());
    }

    private LambdaQueryWrapper<ProjectResult> buildQueryWrapper(ProjectResultBo bo) {
        LambdaQueryWrapper<ProjectResult> lqw = Wrappers.lambdaQuery();

        // 业务类型ID查询
        lqw.eq(bo.getBusinessTypeId() != null, ProjectResult::getBusinessTypeId, bo.getBusinessTypeId());
        // 成果编码查询
        lqw.like(StringUtils.isNotBlank(bo.getResultCode()), ProjectResult::getResultCode, bo.getResultCode());
        // 状态查询
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProjectResult::getStatus, bo.getStatus());
        // 负责项目经理模糊查询
        lqw.like(StringUtils.isNotBlank(bo.getProjectManagers()), ProjectResult::getProjectManagers, bo.getProjectManagers());
        // 项目/任务名称模糊查询
        lqw.like(StringUtils.isNotBlank(bo.getProjectTaskName()), ProjectResult::getProjectTaskName, bo.getProjectTaskName());
        // 成果类型查询
        lqw.eq(StringUtils.isNotBlank(bo.getResultType()), ProjectResult::getResultType, bo.getResultType());
        // 优先级查询
        lqw.eq(StringUtils.isNotBlank(bo.getPriorityLevel()), ProjectResult::getPriorityLevel, bo.getPriorityLevel());
        // 完成时间查询
        lqw.between(bo.getCreateTimeStart() != null && bo.getCreateTimeEnd() != null,
                ProjectResult::getCreatedTime, bo.getCreateTimeStart(), bo.getCreateTimeEnd());

        // 如果明确指定了归档标志，则按指定条件查询；否则默认只查询未归档的数据（archiveFlag != 1）
        if (bo.getArchiveFlag() != null) {
            lqw.eq(ProjectResult::getArchiveFlag, bo.getArchiveFlag());
        } else {
            // 默认不查询已归档的数据
            lqw.eq(ProjectResult::getArchiveFlag, 0);
        }

        return lqw;
    }

    /**
     * 新增项目成果表
     */
    @Override
    public Boolean insertByBo(ProjectResultBo bo) {
        // 处理完成时间业务逻辑
        handleCompletionTime(bo);

        // 生成成果编码
        generateResultCode(bo);

        ProjectResult add = BeanUtil.toBean(bo, ProjectResult.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目成果表
     */
    @Override
    public Boolean updateByBo(ProjectResultBo bo) {
        // 处理完成时间业务逻辑
        handleCompletionTime(bo);

        // 确保成果编码不被修改
        ensureResultCodeNotModified(bo);

        ProjectResult update = BeanUtil.toBean(bo, ProjectResult.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProjectResult entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 处理完成时间业务逻辑
     */
    private void handleCompletionTime(ProjectResultBo bo) {
        // 如果状态不是"已完成"(3)，则将完成时间置空
        if (!CommConstants.CommonValStr.THREE.equals(bo.getStatus())) {
            bo.setCompletionTime(null);
        } else {
            // 如果状态是"已完成"但完成时间为空，则抛出异常
            if (bo.getCompletionTime() == null) {
                throw new ServiceException("状态为已完成时，完成时间不能为空");
            }
        }
    }

    /**
     * 生成成果编码
     */
    private void generateResultCode(ProjectResultBo bo) {
        try {
            // 获取当前日期
            java.util.Date now = new java.util.Date();
            String dateStr = DateUtils.parseDateToStr("yyyyMMdd", now);

            // 获取下一个流水号
            int nextSerial = projectResultNumberService.getNextSerialNumber(dateStr);

            // 生成完整的成果编码
            String resultCode = projectResultNumberService.generateResultCode(dateStr, nextSerial);
            bo.setResultCode(resultCode);

            log.info("生成项目成果编码: {}, 日期: {}, 流水号: {}", resultCode, dateStr, nextSerial);
        } catch (Exception e) {
            throw new ServiceException("生成项目成果编码失败：" + e.getMessage());
        }
    }

    /**
     * 确保成果编码不被修改
     */
    private void ensureResultCodeNotModified(ProjectResultBo bo) {
        if (bo.getId() == null) {
            return;
        }

        // 查询原有记录
        ProjectResult existingRecord = baseMapper.selectById(bo.getId());
        if (existingRecord == null) {
            throw new ServiceException("项目成果不存在");
        }

        // 如果前端传递了成果编码，检查是否与原有记录一致
        if (StringUtils.isNotBlank(bo.getResultCode()) &&
            !bo.getResultCode().equals(existingRecord.getResultCode())) {
            throw new ServiceException("成果编码不允许修改");
        }

        // 确保使用原有的成果编码
        bo.setResultCode(existingRecord.getResultCode());
    }



    /**
     * 批量删除项目成果表
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 归档项目成果
     */
    @Override
    public Boolean archiveById(Long id) {
        // 先查询当前记录
        ProjectResult projectResult = baseMapper.selectById(id);
        if (projectResult == null) {
            throw new ServiceException("项目成果不存在");
        }

        // 检查是否已经归档
        if (projectResult.getArchiveFlag() != null && projectResult.getArchiveFlag() == 1) {
            throw new ServiceException("该项目成果已经归档，无法重复归档");
        }

        // 更新归档标志
        ProjectResult update = new ProjectResult();
        update.setId(id);
        update.setArchiveFlag(1);

        return baseMapper.updateById(update) > 0;
    }


}
