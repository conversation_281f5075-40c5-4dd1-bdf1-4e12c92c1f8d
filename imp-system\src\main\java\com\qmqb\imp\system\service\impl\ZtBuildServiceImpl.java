package com.qmqb.imp.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.system.domain.ZtBuild;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.mapper.ZtBuildMapper;
import com.qmqb.imp.system.service.IZtBuildService;

import java.util.List;
/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
public class ZtBuildServiceImpl implements IZtBuildService {

    private final ZtBuildMapper baseMapper;


    @Override
    public List<ZtBuild> selectByStoryId(Integer id) {
        return baseMapper.selectList(new LambdaQueryWrapper<ZtBuild>()
                .apply("FIND_IN_SET({0}, stories)", id));
    }
}
