package com.qmqb.imp.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;

/**
 * redis key集合
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RedisKey {

    /**
     * dingtalk_access_token:appkey
     */
    DINGTALK_OAPI_ACCESS_TOKEN("dingtalk_oapi:access_token", Duration.ofSeconds(7000)),
    DINGTALK_API_ACCESS_TOKEN("dingtalk_api:access_token", Duration.ofSeconds(7000)),
    /**
     * holiday:date
     */
    HOLIDAY("holiday:%s", Duration.ofDays(1)),

    /**
     * overtime_user::date
     */
    OVERTIME_USER("overtime_user:%s", Duration.ofHours(1)),

    /**
     * dept_name:deptId
     */
    DEPT_NAME("dept_name:%s", Duration.ofDays(1)),

    /**
     * analyze_sql:sqlHash
     */
    ANALYZE_SQL("analyze_sql:%s", Duration.ofMinutes(1)),

    /**
     * release_serial:yyyyMMdd - 发布版本记录流水号
     */
    RELEASE_SERIAL("release_serial:%s", Duration.ofDays(1)),

    /**
     * project_result_serial:yyyyMMdd - 项目成果记录流水号
     */
    PROJECT_RESULT_SERIAL("project_result_serial:%s", Duration.ofDays(1)),

    ;

    /**
     * 键
     */
    private final String key;
    /**
     * 生存时间
     */
    private final Duration ttl;


    public String getRedisKey(Object... args) {
        return String.format(this.getKey(), args);
    }


}
